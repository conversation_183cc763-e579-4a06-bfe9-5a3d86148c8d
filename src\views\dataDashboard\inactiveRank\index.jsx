import React, { useState, useEffect } from 'react'
import { Card, Tag, Tabs } from 'antd'
import GroupedTable from '@/components/GroupedTable'
import TabFilter from '@/components/TabFilter'
import WithWatermark from '../components/WithWatermark'
import { getProjectList93656 } from '@/apis/dataDashboard'

const InactiveRank = () => {
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [tabItems, setTabItems] = useState([
    {
      key: '7',
      label: '',
    },
    {
      key: '15',
      label: '',
    },
    {
      key: '30',
      label: '',
    },
  ])
  const [activeTab, setActiveTab] = useState('7')
  const [regions, setRegions] = useState([])
  const tabValues = {
    7: 'thirtyDeploy',
    15: 'fifteenDeploy',
    30: 'sevenDeploy',
  }
  // 表格列配置
  const columns = [
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '省份',
      dataIndex: 'provinceName',
      key: 'provinceName',
    },
    {
      title: '市级',
      dataIndex: 'cityName',
      key: 'cityName',
    },
    {
      title: '区县',
      dataIndex: 'countyName',
      key: 'countyName',
    },
    {
      title: '已部署时间',
      dataIndex: 'completedDeploymentTime',
      key: 'completedDeploymentTime',
    },
    {
      title: '租户活跃度',
      dataIndex: `sevenDayTenantCount${activeTab}Active`,
      key: `sevenDayTenantCount${activeTab}Active`,
      render: (activity, record) => (
        <div>
          {/* 租户活跃度指标 */}
          <div className="flex justify-between items-center">
            <span>
              {record[`sevenDayTenantCount${activeTab}Active`]}/
              {record.tenantSum}
            </span>
            <div className="relative w-[60px] bg-[#eee] h-6 rounded-4 overflow-hidden">
              <div
                className="absolute h-6 bg-[#FF5555]"
                style={{
                  width: `${record.tenantSum != 0 ? (record[`sevenDayTenantCount${activeTab}Active`] / record.tenantSum) * 100 : 0}%`,
                }}
              ></div>
            </div>
          </div>

          <span className="text-[#999] text-12">
            未活跃：
            {record.tenantSum - record[`sevenDayTenantCount${activeTab}Active`]}
          </span>
        </div>
      ),
    },
    {
      title: '用户活跃度',
      dataIndex: `sevenDayUserCount${activeTab}Active`,
      key: `sevenDayUserCount${activeTab}Active`,
      render: (activity, record) => (
        <div>
          {/* 租户活跃度指标 */}
          <div className="flex justify-between items-center">
            <span>
              {record[`sevenDayUserCount${activeTab}Active`]}/{record.userSum}
            </span>
            <div className="relative w-[60px] bg-[#eee] h-6 rounded-4 overflow-hidden">
              <div
                className="absolute h-6 bg-[#FF5555]"
                style={{
                  width: `${record.userSum != 0 ? (record[`sevenDayUserCount${activeTab}Active`] / record.userSum) * 100 : 0}%`,
                }}
              ></div>
            </div>
          </div>

          <span className="text-[#999] text-12">
            未活跃：
            {record.userSum - record[`sevenDayUserCount${activeTab}Active`]}
          </span>
        </div>
      ),
    },
    {
      title: '省负责人',
      dataIndex: 'manageUserDto',
      key: 'manageUserDto',
      render: (activity) => (
        <div>
          <div>{activity && activity.length ? activity[0].userName : '--'}</div>
          <div className="text-[#999] text-12">
            {activity && activity.length ? activity[0].mobile : '--'}
          </div>
        </div>
      ),
    },
    {
      title: '地市负责人',
      dataIndex: 'manageCityUserDto',
      key: 'manageCityUserDto',
      render: (activity) => (
        <div>
          <div>{activity && activity.length ? activity[0].userName : '--'}</div>
          <div className="text-[#999] text-12">
            {activity && activity.length ? activity[0].mobile : '--'}
          </div>
        </div>
      ),
    },
  ]
  const getProjectList = async (params = {}) => {
    setLoading(true)
    const {
      pageIndex = pagination.current,
      pageSize = pagination.pageSize,
      days,
    } = params

    try {
      const {
        data: { projectList, projectListInfo, total },
      } = await getProjectList93656({
        type: '3',
        days,
        pageIndex: 1,
        pageSize: 30,
      })
      setTabItems((pre) => {
        return pre.map((item) => {
          item.label = `${item.key}天未活跃`
          return item
        })
      })
      console.log('projectList', projectList)
      setDataSource(projectList)
      setPagination({
        current: pageIndex,
        pageSize,
        total,
      })
    } catch (error) {
      console.error('获取项目列表失败:', error)
    } finally {
      setLoading(false)
    }
  }
  // 根据选项卡选择获取数据
  useEffect(() => {
    getProjectList({ days: activeTab, pageIndex: 1 })
  }, [activeTab])

  // 处理搜索
  const handleSearch = (values) => {
    console.log('搜索条件:', values)
    getProjectList({
      ...values,
      pageIndex: values.current || pagination.current,
      days: activeTab,
    })
  }

  return (
    <Card className="relative pt-50px">
      <TabFilter
        className="absolute top-16 left-16"
        items={tabItems}
        activeKey={activeTab}
        onChange={setActiveTab}
      />
      <GroupedTable
        columns={columns}
        dataSource={dataSource}
        onSearch={handleSearch}
        pagination={false}
        scroll={{ y: window.innerHeight - 360 }}
        loading={loading}
        fieldAlign="end"
        tableProps={{
          rowKey: 'key',
        }}
      />
    </Card>
  )
}

// 使用水印高阶组件包装InactiveRank
export default WithWatermark(InactiveRank)
