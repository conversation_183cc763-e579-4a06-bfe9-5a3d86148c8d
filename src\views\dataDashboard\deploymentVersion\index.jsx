import React, { useState, useEffect } from 'react'
import { Card, Table, Tag } from 'antd'
import GroupedTable from '@/components/GroupedTable'
import VersionPieChart from '../components/VersionPieChart'
import WithWatermark from '../components/WithWatermark'

import {
  getProjectList93656,
  getProjectVersion93658,
} from '@/apis/dataDashboard'
const DeploymentVersion = () => {
  const [versionFilter, setVersionFilter] = useState()
  const [versionData, setVersionData] = useState([])
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const [queryFields, setQueryFields] = useState()
  const [total, setTotal] = useState(0)
  const [currentPagination, setCurrentPagination] = useState({
    current: 1,
    pageSize: 10,
  })
  const columns = [
    { title: '客户名称', dataIndex: 'customerName' },
    {
      title: '是否付费',
      dataIndex: 'pay',
      render: (value) => (
        <Tag color={value ? 'success' : 'default'}>{value ? '是' : '否'}</Tag>
      ),
    },
    { title: '省份', dataIndex: 'provinceName' },
    { title: '市级', dataIndex: 'cityName' },
    { title: '区县', dataIndex: 'countyName' },
    { title: '服务器数量', dataIndex: 'gpuServerCount' },
    { title: '模型版本', dataIndex: 'modelVersionName' },
    { title: 'GPU品牌', dataIndex: 'gpuBrandName' },
    { title: 'GPU型号', dataIndex: 'gpuTypeName' },
    { title: '渠道来源', dataIndex: 'channelName' },
    {
      title: '线索等级',
      dataIndex: 'saleClue',
      width: 100,
    },
  ]
  const getVersionData = async () => {
    const {
      data: { versionGroup },
    } = await getProjectVersion93658()
    console.log('versionList', versionGroup)
    const versionList = versionGroup.map((item) => ({
      name: item.versionName,
      value: item.versionNum,
    }))
    setVersionData(versionList)
    setQueryFields([
      {
        label: '版本',
        name: 'version',
        type: 'select',
        options: versionList.map((item) => ({
          value: item.name,
          label: item.name,
        })),
      },
    ])
  }
  const getProjectList = async (params = {}) => {
    setLoading(true)
    const {
      pageIndex = currentPagination.current,
      pageSize = currentPagination.pageSize,
      version,
    } = params

    try {
      const {
        data: { projectList, total },
      } = await getProjectList93656({
        type: '2',
        pageIndex,
        pageSize,
        version, // 传递版本参数给后端
      })
      console.log('projectList', projectList)
      setDataSource(projectList)
      setTotal(total)
      setCurrentPagination({
        current: pageIndex,
        pageSize,
      })
    } catch (error) {
      console.error('获取项目列表失败:', error)
    } finally {
      setLoading(false)
    }
  }
  // 初始加载数据
  useEffect(() => {
    getVersionData()
    getProjectList()
  }, [])

  return (
    <div className="pb-60px">
      <Card className="mb-24 p-16">
        <h1 className="text-20 font-500 mb-24">版本分布统计</h1>
        <VersionPieChart data={versionData} />
      </Card>

      <div className="p-16 pb-0 bg-white rounded-8 ">
        <GroupedTable
          queryFields={queryFields}
          initialValues={{ version: versionFilter }}
          onSearch={(values) => {
            const { version, pageSize, current: pageIndex } = values
            console.log('values', values)
            setVersionFilter(version)
            getProjectList({
              version,
              pageSize,
              pageIndex,
            })
          }}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 'max-content' }}
          pagination={{
            current: currentPagination.current,
            pageSize: currentPagination.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `总条数: ${total}`,
          }}
          loading={loading}
        />
      </div>
    </div>
  )
}

// 使用水印高阶组件包装DeploymentVersion
export default WithWatermark(DeploymentVersion)
