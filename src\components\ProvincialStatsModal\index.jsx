import React from 'react'
import { Modal, Table } from 'antd'
import ReactEcharts from 'echarts-for-react'
import { PROJECT_STATUS_CONFIG } from 'views/dataDashboard/config'
import './style.css'

/**
 * 省级统计模态框组件
 *
 * @param {boolean} open - 控制模态框的可见性
 * @param {function} onCancel - 模态框关闭时调用的函数
 * @param {Array} provincialStatsData - 省级统计数据数组
 * @param {Object} totalStats - 包含总计统计数据的对象
 * @param {string} title - 模态框标题
 * @param {string} width - 模态框宽度
 */
const ProvincialStatsModal = ({
  open,
  onCancel,
  provincialStatsData = [],
  totalStats = {},
  title = '各省份部署情况统计',
  width = '1200px',
}) => {
  // 省级统计表格列配置
  const statsColumns = [
    {
      title: '地区',
      dataIndex: 'areaName',
      key: 'areaName',
    },
    {
      title: '已部署',
      dataIndex: 'COMPLETED',
      key: 'COMPLETED',
    },
    {
      title: '部署中',
      dataIndex: 'NOT_DEPLOYED',
      key: 'NOT_DEPLOYED',
    },
    {
      title: '资源下发中',
      dataIndex: 'DEPLOYING',
      key: 'DEPLOYING',
    },
    {
      title: '洽谈中',
      dataIndex: 'NEGOTIATION',
      key: 'NEGOTIATION',
    },
    {
      title: '合计',
      dataIndex: 'total',
      key: 'total',
    },
  ]

  // 水平条形图的图表选项 - 已更新以匹配设计
  const getChartOption = () => {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        data: ['已部署', '部署中', '资源下发中', '洽谈中'],
        top: 10,
        left: 20,
        icon: 'circle',
        itemWidth: 6,
        itemHeight: 6,
        textStyle: {
          fontSize: 12,
        },
      },
      grid: {
        left: '3%',
        right: '15%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: true, lineStyle: { type: 'dashed' } },
        // 最大值用每条数据里的COMPLETED，NOT_DEPLOYED，DEPLOYING，NEGOTIATION最大值除以0.8取整十
        max: Math.ceil(
          Math.max(
            ...provincialStatsData.map(
              (item) =>
                item.COMPLETED +
                item.NOT_DEPLOYED +
                item.DEPLOYING +
                item.NEGOTIATION,
            ),
          ) / 0.8,
        ),
        axisLabel: {
          formatter: '{value}',
        },
      },
      yAxis: {
        type: 'category',
        data: provincialStatsData.map((item) => item.areaName),
        axisLine: { show: false },
        axisTick: { show: false },
        inverse: true,
      },
      dataZoom: [
        {
          type: 'inside',
          show: false,
          start: 0,
          end: 15,
          orient: 'vertical',
          zoomOnMouseWheel: false,
          moveOnMouseWheel: true,
        },
      ],
      series: PROJECT_STATUS_CONFIG.STATUS_DATA.map((status) => ({
        name: status.name,
        type: 'bar',
        stack: false,
        barWidth: 10,
        barGap: '30%',
        emphasis: { focus: 'series' },
        data: provincialStatsData.map((item) => item[status.statusKey]),
        itemStyle: {
          color: status.color,
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
        },
      })),
    }
  }

  return (
    <Modal
      title={title}
      open={open}
      onCancel={onCancel}
      width={width}
      footer={null}
    >
      <div className="provincial-stats-container">
        <div className="provincial-stats-table-container">
          <Table
            columns={statsColumns}
            dataSource={provincialStatsData}
            pagination={false}
            size="middle"
            bordered
            scroll={{ y: 407 }}
            sticky
            summary={() => (
              <Table.Summary fixed="bottom">
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>合计</Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>
                    {totalStats.COMPLETED}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                    {totalStats.NOT_DEPLOYED}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3}>
                    {totalStats.DEPLOYING}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={4}>
                    {totalStats.NEGOTIATION}
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={5}>
                    {totalStats.total}
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
            className="provincial-stats-table"
          />
        </div>
        <div
          className="provincial-stats-chart-container rounded-4"
          style={{ border: '1px solid #eee' }}
        >
          <ReactEcharts
            option={getChartOption()}
            style={{ height: '498px', width: '558px' }}
            className="provincial-stats-chart"
          />
        </div>
      </div>
    </Modal>
  )
}

export default ProvincialStatsModal
