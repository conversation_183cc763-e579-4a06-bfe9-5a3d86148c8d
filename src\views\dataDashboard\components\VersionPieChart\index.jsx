import React, { useMemo } from 'react'
import ReactEcharts from 'echarts-for-react'

const VersionPieChart = ({ data = [] }) => {
  // 饼图颜色
  const pieColors = [
    '#4574FF',
    '#FFBB33',
    '#3DCCE1',
    '#15D888',
    '#9254DE',
    '#FF7A45',
  ]

  const chartOption = useMemo(() => {
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          center: ['50%', '50%'],
          data: data.map((item, index) => ({
            name: item.name,
            value: item.value,
            itemStyle: {
              color: pieColors[index % pieColors.length],
            },
          })),
          label: {
            show: true,
            formatter: (params) => {
              // 找到对应的数据项
              const dataItem = data.find((item) => item.name === params.name)
              return [
                `{value|${params.value}}个 ${dataItem?.percent || ''}`,
                `{name|版本: ${params.name}}`,
              ].join('\n')
            },
            rich: {
              value: {
                fontSize: 14,
                fontWeight: 'bold',
                lineHeight: 20,
              },
              name: {
                fontSize: 12,
                color: '#999',
                lineHeight: 16,
              },
            },
            position: 'outside',
            alignTo: 'edge',
            edgeDistance: 20,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }
  }, [data])

  return (
    <ReactEcharts
      option={chartOption}
      style={{ height: '320px', maxWidth: '800px', margin: '0 auto' }}
      className="w-full"
    />
  )
}

export default VersionPieChart
